package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.model.UserPreferences
import com.focusflow.data.repository.UserPreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()

    init {
        loadUserPreferences()
    }

    private fun loadUserPreferences() {
        viewModelScope.launch {
            try {
                userPreferencesRepository.getUserPreferences().collect { preferences ->
                    preferences?.let {
                        _uiState.value = _uiState.value.copy(
                            userPreferences = it,
                            isLoading = false,
                            error = null
                        )
                    } ?: run {
                        // Create default preferences if none exist
                        val defaultPrefs = UserPreferences()
                        userPreferencesRepository.insertUserPreferences(defaultPrefs)
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load preferences: ${e.message}"
                )
            }
        }
    }

    fun updateTheme(theme: String) {
        viewModelScope.launch {
            try {
                val currentPrefs = _uiState.value.userPreferences
                val updatedPrefs = currentPrefs.copy(theme = theme)
                userPreferencesRepository.updateUserPreferences(updatedPrefs)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update theme: ${e.message}"
                )
            }
        }
    }

    fun updateFontSize(fontSize: String) {
        viewModelScope.launch {
            try {
                userPreferencesRepository.updateFontSize(fontSize)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update font size: ${e.message}"
                )
            }
        }
    }

    fun updateNotificationsEnabled(enabled: Boolean) {
        viewModelScope.launch {
            try {
                userPreferencesRepository.updateNotificationsEnabled(enabled)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update notifications: ${e.message}"
                )
            }
        }
    }

    fun updateNotificationTime(time: String) {
        viewModelScope.launch {
            try {
                val currentPrefs = _uiState.value.userPreferences
                val updatedPrefs = currentPrefs.copy(reminderTime = time, notificationTime = time)
                userPreferencesRepository.updateUserPreferences(updatedPrefs)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update notification time: ${e.message}"
                )
            }
        }
    }

    fun updateBudgetPeriod(period: String) {
        viewModelScope.launch {
            try {
                userPreferencesRepository.updateBudgetPeriod(period)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update budget period: ${e.message}"
                )
            }
        }
    }

    fun updatePrimaryGoal(goal: String?) {
        viewModelScope.launch {
            try {
                val currentPrefs = _uiState.value.userPreferences
                val updatedPrefs = currentPrefs.copy(primaryGoal = goal)
                userPreferencesRepository.updateUserPreferences(updatedPrefs)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update primary goal: ${e.message}"
                )
            }
        }
    }

    fun exportData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isExporting = true)
                // TODO: Implement data export functionality
                // This will be implemented in Phase 4
                _uiState.value = _uiState.value.copy(
                    isExporting = false,
                    exportMessage = "Data export feature coming soon!"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isExporting = false,
                    error = "Failed to export data: ${e.message}"
                )
            }
        }
    }

    fun clearData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isClearing = true)
                // TODO: Implement data clearing functionality
                // This will be implemented in Phase 4
                _uiState.value = _uiState.value.copy(
                    isClearing = false,
                    clearMessage = "Data clearing feature coming soon!"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isClearing = false,
                    error = "Failed to clear data: ${e.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun clearMessages() {
        _uiState.value = _uiState.value.copy(
            exportMessage = null,
            clearMessage = null
        )
    }
}

data class SettingsUiState(
    val userPreferences: UserPreferences = UserPreferences(),
    val isLoading: Boolean = true,
    val isExporting: Boolean = false,
    val isClearing: Boolean = false,
    val error: String? = null,
    val exportMessage: String? = null,
    val clearMessage: String? = null
)
